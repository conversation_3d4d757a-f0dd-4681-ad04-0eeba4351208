<script setup lang="ts">
import { computed, onBeforeUnmount, ref, shallowRef, toRef, watch, onMounted } from 'vue'
import { AnnotationLayer, TextLayer } from 'pdfjs-dist/legacy/build/pdf.mjs'
import { PDFLinkService } from 'pdfjs-dist/web/pdf_viewer.mjs'
import type {
  OnProgressParameters,
  PDFDocumentProxy,
  PDFPageProxy,
  PageViewport,
} from 'pdfjs-dist'

import type { PasswordRequestParams, Source } from './types'
import {
  addPrintStyles,
  createPrintIframe,
  downloadPdf,
  emptyElement,
  releaseChildCanvases,
} from './utils'
import { useVuePdfEmbed } from './composables'

// 定义高亮数据类型
interface Highlight {
  pageNumber: number
  x: number
  y: number
  width: number
  height: number
  text: string
  pageWidth: number
  pageHeight: number
}

// 定义跨页文本搜索相关类型
interface PageTextInfo {
  pageNumber: number
  textContent: string
  textElements: HTMLElement[]
  elementMap: TextElementInfo[]
  container: HTMLElement
  viewport: PageViewport
}

interface TextElementInfo {
  element: HTMLElement
  startPos: number
  endPos: number
  text: string
  index: number
  pageNumber: number
}

interface CrossPageMatch {
  text: string
  startPage: number
  endPage: number
  startPos: number
  endPos: number
  segments: MatchSegment[]
}

interface MatchSegment {
  pageNumber: number
  startPos: number
  endPos: number
  text: string
  elements: TextElementInfo[]
}

const props = withDefaults(
  defineProps<{
    /**
     * 是否启用注释层
     */
    annotationLayer?: boolean
    /**
     * 期望的页面高度
     */
    height?: number
    /**
     * 根元素标识符（由带有页码后缀的页面容器继承）
     */
    id?: string
    /**
     * 注释图标的路径，包括尾部斜杠
     */
    imageResourcesPath?: string
    /**
     * 文档导航服务
     */
    linkService?: PDFLinkService
    /**
     * 要显示的页码
     */
    page?: number
    /**
     * 期望的页面旋转角度
     */
    rotation?: number
    /**
     * 画布大小与文档大小的期望比例
     */
    scale?: number
    /**
     * 要显示的文档源
     */
    source: Source
    /**
     * 是否启用文本层
     */
    textLayer?: boolean
    /**
     * 期望的页面宽度
     */
    width?: number
    /**
     * 是否显示所有页面
     */
    allPages?: boolean
    /**
     * 要显示的高亮数组
     */
    highlights?: Highlight[]
    /**
     * 高亮文本 - 用于前端文字搜索
     */
    highlightText?: string
  }>(),
  {
    rotation: 0,
    scale: 1.0,  // 增加默认缩放比例
    width: 800,  // 设置默认宽度
    allPages: false,
    highlights: () => [],
    highlightText: ''
  }
)

const emit = defineEmits<{
  (e: 'internal-link-clicked', value: number): void
  (e: 'loaded', value: PDFDocumentProxy): void
  (e: 'loading-failed', value: Error): void
  (e: 'password-requested', value: PasswordRequestParams): void
  (e: 'progress', value: OnProgressParameters): void
  (e: 'rendered'): void
  (e: 'rendering-failed', value: Error): void
  (e: 'text-selected', value: { text: string; pageNumber: number; fileId?: number }): void
}>()

const pageNums = shallowRef<number[]>([])
const pageScales = ref<number[]>([])
const root = shallowRef<HTMLDivElement | null>(null)

let renderingController: { isAborted: boolean; promise: Promise<void> } | null =
  null

const { doc } = useVuePdfEmbed({
  onError: (e) => {
    pageNums.value = []
    emit('loading-failed', e)
  },
  onPasswordRequest({ callback, isWrongPassword }) {
    emit('password-requested', { callback, isWrongPassword })
  },
  onProgress: (progressParams) => {
    emit('progress', progressParams)
  },
  source: toRef(props, 'source'),
})

const linkService = computed(() => {
  if (!doc.value || !props.annotationLayer) {
    return null
  } else if (props.linkService) {
    return props.linkService
  }

  const service = new PDFLinkService()
  service.setDocument(doc.value)
  service.setViewer({
    scrollPageIntoView: ({ pageNumber }: { pageNumber: number }) => {
      emit('internal-link-clicked', pageNumber)
    },
  })
  return service
})

/**
 * 下载PDF文档
 * @param filename - 预定义的文件名
 */
const download = async (filename: string) => {
  if (!doc.value) {
    return
  }

  const data = await doc.value.getData()
  const metadata = await doc.value.getMetadata()
  const suggestedFilename =
    // @ts-expect-error: contentDispositionFilename 未定义类型
    filename ?? metadata.contentDispositionFilename ?? ''
  downloadPdf(data, suggestedFilename)
}

/**
 * 根据props和宽高比返回实际页面宽度和高度的数组
 * @param ratio - 页面宽高比
 */
const getPageDimensions = (ratio: number): [number, number] => {
  let width: number
  let height: number

  if (props.height && !props.width) {
    height = props.height
    width = height / ratio
  } else {
    width = props.width ?? root.value!.clientWidth
    height = width * ratio
  }

  return [width, height]
}

/**
 * 通过浏览器界面打印PDF文档
 * @param dpi - 打印分辨率
 * @param filename - 预定义的文件名
 * @param allPages - 是否忽略page属性并打印所有页面
 */
const print = async (dpi = 300, filename = '', allPages = false) => {
  if (!doc.value) {
    return
  }

  const printUnits = dpi / 72
  const styleUnits = 96 / 72
  let container: HTMLDivElement
  let iframe: HTMLIFrameElement
  let title: string | undefined

  try {
    container = window.document.createElement('div')
    container.style.display = 'none'
    window.document.body.appendChild(container)
    iframe = await createPrintIframe(container)

    const pageNums =
      props.page && !allPages
        ? [props.page]
        : [...Array(doc.value.numPages + 1).keys()].slice(1)

    await Promise.all(
      pageNums.map(async (pageNum, i) => {
        const page = await doc.value!.getPage(pageNum)
        const viewport = page.getViewport({
          scale: 1,
          rotation: 0,
        })

        if (i === 0) {
          const sizeX = (viewport.width * printUnits) / styleUnits
          const sizeY = (viewport.height * printUnits) / styleUnits
          addPrintStyles(iframe, sizeX, sizeY)
        }

        const canvas = window.document.createElement('canvas')
        canvas.width = viewport.width * printUnits
        canvas.height = viewport.height * printUnits
        container.appendChild(canvas)
        const canvasClone = canvas.cloneNode() as HTMLCanvasElement
        iframe.contentWindow!.document.body.appendChild(canvasClone)

        await page.render({
          canvasContext: canvas.getContext('2d')!,
          intent: 'print',
          transform: [printUnits, 0, 0, printUnits, 0, 0],
          viewport,
        }).promise

        canvasClone.getContext('2d')!.drawImage(canvas, 0, 0)
      })
    )

    if (filename) {
      title = window.document.title
      window.document.title = filename
    }

    iframe.contentWindow?.focus()
    iframe.contentWindow?.print()
  } finally {
    if (title) {
      window.document.title = title
    }

    releaseChildCanvases(container!)
    container!.parentNode?.removeChild(container!)
  }
}

/**
 * 将PDF文档渲染为canvas元素和附加层
 */
const render = async () => {
  if (!doc.value || renderingController?.isAborted) {
    return
  }

  try {
    pageNums.value = props.allPages
      ? [...Array(doc.value.numPages + 1).keys()].slice(1)
      : props.page
      ? [props.page]
      : [1]
    pageScales.value = Array(pageNums.value.length).fill(1)

    await Promise.all(
      pageNums.value.map(async (pageNum, i) => {
        const page = await doc.value!.getPage(pageNum)
        if (renderingController?.isAborted) {
          return
        }
        const pageRotation =
          ((props.rotation % 90 === 0 ? props.rotation : 0) + page.rotate) % 360
        const [canvas, div1, div2] = Array.from(
          root.value!.getElementsByClassName('vue-pdf-embed__page')[i].children
        ) as [HTMLCanvasElement, HTMLDivElement, HTMLDivElement]
        const isTransposed = !!((pageRotation / 90) % 2)
        const viewWidth = page.view[2] - page.view[0]
        const viewHeight = page.view[3] - page.view[1]
        const [actualWidth, actualHeight] = getPageDimensions(
          isTransposed ? viewWidth / viewHeight : viewHeight / viewWidth
        )
        const cssWidth = `${Math.floor(actualWidth)}px`
        const cssHeight = `${Math.floor(actualHeight)}px`
        const pageWidth = isTransposed ? viewHeight : viewWidth
        const pageScale = actualWidth / pageWidth
        const viewport = page.getViewport({
          scale: pageScale,
          rotation: pageRotation,
        })

        pageScales.value[i] = pageScale

        canvas.style.display = 'block'
        canvas.style.width = cssWidth
        canvas.style.height = cssHeight

        const renderTasks = [
          renderPage(
            page,
            viewport.clone({
              scale: viewport.scale * window.devicePixelRatio * props.scale,
            }),
            canvas
          ),
        ]

        if (props.textLayer) {
          renderTasks.push(
            renderPageTextLayer(
              page,
              viewport.clone({
                dontFlip: true,
              }),
              div1
            )
          )
        }

        if (props.annotationLayer) {
          renderTasks.push(
            renderPageAnnotationLayer(
              page,
              viewport.clone({
                dontFlip: true,
              }),
              div2 || div1
            )
          )
        }

        return Promise.all(renderTasks)
      })
    )

    if (!renderingController?.isAborted) {
      emit('rendered')
    }
  } catch (e) {
    pageNums.value = []
    pageScales.value = []

    if (!renderingController?.isAborted) {
      emit('rendering-failed', e as Error)
    }
  }
}

/**
 * 渲染页面内容
 * @param page - 页面代理
 * @param viewport - 页面视口
 * @param canvas - HTML画布
 */
const renderPage = async (
  page: PDFPageProxy,
  viewport: PageViewport,
  canvas: HTMLCanvasElement
) => {
  canvas.width = viewport.width
  canvas.height = viewport.height
  await page.render({
    canvasContext: canvas.getContext('2d')!,
    viewport,
  }).promise
}

/**
 * 为指定页面渲染注释层
 * @param page - 页面代理
 * @param viewport - 页面视口
 * @param container - HTML容器
 */
const renderPageAnnotationLayer = async (
  page: PDFPageProxy,
  viewport: PageViewport,
  container: HTMLDivElement
) => {
  emptyElement(container)
  new AnnotationLayer({
    accessibilityManager: null,
    annotationCanvasMap: null,
    annotationEditorUIManager: null,
    div: container,
    page,
    structTreeLayer: null,
    viewport,
  }).render({
    annotations: await page.getAnnotations(),
    div: container,
    imageResourcesPath: props.imageResourcesPath,
    linkService: linkService.value!,
    page,
    renderForms: false,
    viewport,
  })
}

/**
 * 为指定页面渲染文本层
 * @param page - 页面代理
 * @param viewport - 页面视口
 * @param container - HTML容器
 */
const renderPageTextLayer = async (
  page: PDFPageProxy,
  viewport: PageViewport,
  container: HTMLElement
) => {
  emptyElement(container)
  new TextLayer({
    container,
    textContentSource: await page.getTextContent(),
    viewport,
  }).render()

  // 渲染文字搜索高亮
  if (props.highlightText) {
    await renderTextSearchHighlights(page, container, viewport, props.highlightText)
  }
  
  // 添加文字选择事件监听
  addTextSelectionListener(container, page.pageNumber)
}

/**
 * 添加文字选择事件监听
 * @param container - 文本层容器
 * @param pageNumber - 页码
 */
const addTextSelectionListener = (container: HTMLElement, pageNumber: number) => {
  container.addEventListener('mouseup', () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim()
      if (selectedText.length > 0) {
        emit('text-selected', {
          text: selectedText,
          pageNumber: pageNumber
        })
      }
    }
  })
}

/**
 * 渲染文字搜索高亮
 * @param page - 页面代理
 * @param container - 文本层容器
 * @param viewport - 页面视口
 * @param highlightText - 高亮文本
 */
const renderTextSearchHighlights = async (
  page: PDFPageProxy,
  container: HTMLElement,
  viewport: PageViewport,
  highlightText: string
) => {
  try {
    // 清理之前的文字搜索高亮
    clearPreviousTextHighlights(container)
    
    // 验证输入参数
    if (!highlightText.trim()) {
      console.warn('高亮文本为空，跳过文字搜索')
      return
    }

    console.log('开始文字搜索高亮:', highlightText)

    // 等待文本层完全渲染
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 获取所有文本元素
    const textElements = container.querySelectorAll('.textLayer > span')
    if (!textElements || textElements.length === 0) {
      console.warn('未找到文本元素，无法进行文字搜索高亮')
      return
    }

    console.log(`找到 ${textElements.length} 个文本元素`)

    // 处理换行符，支持跨行文本
    const normalizedHighlightText = highlightText.replace(/\n/g, '').replace(/\s+/g, ' ').trim()
    
    // 将所有文本元素按位置排序
    const textArray = Array.from(textElements as NodeListOf<HTMLElement>)
    textArray.sort((a, b) => {
      const aRect = a.getBoundingClientRect()
      const bRect = b.getBoundingClientRect()
      // 首先按Y坐标排序（从上到下）
      const yDiff = aRect.top - bRect.top
      if (Math.abs(yDiff) > 5) {
        return yDiff
      }
      // 同一行内按X坐标排序（从左到右）
      return aRect.left - bRect.left
    })

    // 构建完整文本和元素映射
    let fullText = ''
    const elementMap = []
    
    textArray.forEach((element, index) => {
      const text = element.textContent || ''
      const startPos = fullText.length
      // 移除多余的空格和换行
      const cleanText = text.replace(/\s+/g, ' ')
      fullText += cleanText
      const endPos = fullText.length
      
      elementMap.push({
        element,
        startPos,
        endPos,
        text: cleanText,
        index
      })
    })

    console.log('构建的完整文本:', fullText.substring(0, 200) + '...')
    console.log('搜索文本:', normalizedHighlightText)

    // 查找匹配项
    const regex = new RegExp(escapeRegExp(normalizedHighlightText), 'gi')
    const matches = []
    let match
    
    while ((match = regex.exec(fullText)) !== null) {
      matches.push({
        start: match.index,
        end: match.index + match[0].length,
        text: match[0]
      })
      // 防止无限循环
      if (match.index === regex.lastIndex) {
        regex.lastIndex++
      }
    }

    console.log(`找到 ${matches.length} 个匹配项:`, matches)

    // 为每个匹配项创建高亮
    matches.forEach((match, matchIndex) => {
      // 找到匹配文本涉及的所有元素
      const involvedElements = elementMap.filter(item => 
        item.startPos < match.end && item.endPos > match.start
      )

      console.log(`匹配项 ${matchIndex + 1} 涉及的元素:`, involvedElements.length)

      if (involvedElements.length > 0) {
        // 为每个涉及的元素计算精确的高亮区域
        involvedElements.forEach(item => {
          const element = item.element
          const elementText = item.text
          
          // 计算在当前元素内的匹配部分
          const matchStartInElement = Math.max(0, match.start - item.startPos)
          const matchEndInElement = Math.min(elementText.length, match.end - item.startPos)
          
          if (matchStartInElement < matchEndInElement) {
            // 获取匹配的文本部分
            const matchedTextInElement = elementText.substring(matchStartInElement, matchEndInElement)
            
            // 获取元素的位置和样式信息
            const rect = element.getBoundingClientRect()
            const containerRect = container.getBoundingClientRect()
            const computedStyle = window.getComputedStyle(element)
            
            // 创建临时元素来测量文本宽度
            const measureElement = document.createElement('span')
            measureElement.style.font = computedStyle.font
            measureElement.style.fontSize = computedStyle.fontSize
            measureElement.style.fontFamily = computedStyle.fontFamily
            measureElement.style.fontWeight = computedStyle.fontWeight
            measureElement.style.letterSpacing = computedStyle.letterSpacing
            measureElement.style.visibility = 'hidden'
            measureElement.style.position = 'absolute'
            measureElement.style.whiteSpace = 'nowrap'
            document.body.appendChild(measureElement)
            
            // 测量匹配开始位置之前的文本宽度
            const beforeText = elementText.substring(0, matchStartInElement)
            measureElement.textContent = beforeText
            const beforeWidth = measureElement.offsetWidth
            
            // 测量匹配文本的宽度
            measureElement.textContent = matchedTextInElement
            const matchWidth = measureElement.offsetWidth
            
            // 清理测量元素
            document.body.removeChild(measureElement)
            
            // 计算高亮区域的精确位置
            const highlightLeft = rect.left - containerRect.left + beforeWidth
            const highlightTop = rect.top - containerRect.top
            const highlightWidth = matchWidth
            const highlightHeight = rect.height
            
            // 创建高亮覆盖层
            const highlightOverlay = document.createElement('div')
            highlightOverlay.className = 'pdf-text-search-highlight'
            highlightOverlay.style.position = 'absolute'
            highlightOverlay.style.left = `${highlightLeft}px`
            highlightOverlay.style.top = `${highlightTop}px`
            highlightOverlay.style.width = `${highlightWidth}px`
            highlightOverlay.style.height = `${highlightHeight}px`
            highlightOverlay.style.backgroundColor = 'rgba(255, 255, 0, 0.8) !important'
            highlightOverlay.style.pointerEvents = 'none'
            highlightOverlay.style.zIndex = '2'
            highlightOverlay.style.borderRadius = '2px'
            highlightOverlay.style.transition = 'all 0.2s ease'
            highlightOverlay.style.border = '1px solid rgba(255, 200, 0, 0.6)'
            
            container.appendChild(highlightOverlay)
            
            console.log(`创建精确高亮:`, {
              elementText,
              matchedText: matchedTextInElement,
              matchStartInElement,
              matchEndInElement,
              beforeWidth,
              matchWidth,
              position: {
                left: highlightLeft,
                top: highlightTop,
                width: highlightWidth,
                height: highlightHeight
              }
            })
          }
        })
      }
    })

    // 如果找到匹配项，滚动到第一个
    if (matches.length > 0) {
      setTimeout(() => {
        const firstHighlight = container.querySelector('.pdf-text-search-highlight')
        if (firstHighlight) {
          firstHighlight.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          })
        }
      }, 100)
    }
    
  } catch (error) {
    console.error('文字搜索高亮渲染失败:', error)
  }
}

/**
 * 清理之前的文字搜索高亮
 * @param container - 文本层容器
 */
const clearPreviousTextHighlights = (container: HTMLElement) => {
  // 清理文字搜索高亮
  const searchHighlights = container.querySelectorAll('.pdf-text-search-highlight')
  searchHighlights.forEach(highlight => {
    highlight.remove()
  })
}

/**
 * 转义正则表达式特殊字符
 * @param string - 需要转义的字符串
 */
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

watch(
  doc,
  (newDoc) => {
    if (newDoc) {
      emit('loaded', newDoc)
    }
  },
  { immediate: true }
)

watch(
  () => [
    doc.value,
    props.annotationLayer,
    props.height,
    props.imageResourcesPath,
    props.page,
    props.rotation,
    props.scale,
    props.textLayer,
    props.width,
    props.highlightText
  ],
  async ([newDoc]) => {
    if (newDoc) {
      if (renderingController) {
        renderingController.isAborted = true
        await renderingController.promise
      }

      releaseChildCanvases(root.value)
      renderingController = {
        isAborted: false,
        promise: render(),
      }

      await renderingController.promise
      renderingController = null
    }
  },
  { immediate: true }
)

// watch(() => props.page, (newPage) => {
//   if (root.value) {
//     const pages = root.value.getElementsByClassName('vue-pdf-embed__page')
//     if (pages && pages[newPage - 1]) {
//       pages[newPage - 1].scrollIntoView({ behavior: 'smooth' })
//     }
//   }
// })

onBeforeUnmount(() => {
  releaseChildCanvases(root.value)
})

onMounted(() => {
  if (root.value) {
    setTimeout(() => {
      const pages = root.value?.getElementsByClassName('vue-pdf-embed__page')
      if (pages) {
        console.log('VuePdfEmbed onMounted called')
        // 使用当前页码进行滚动
        if (props.page && pages[props.page - 1]) {
          pages[props.page - 1].scrollIntoView({ behavior: 'smooth' })
        }
      }
    }, 500)
  }
})

defineExpose({
  doc,
  download,
  print
})
</script>

<template>
  <div :id="id" ref="root" class="vue-pdf-embed">
    <div v-for="(pageNum, i) in pageNums" :key="pageNum">
      <slot name="before-page" :page="pageNum" />

      <div
        :id="id && `${id}-${pageNum}`"
        class="vue-pdf-embed__page"
        :style="{
          '--scale-factor': pageScales[i],
          position: 'relative',
        }"
      >
        <canvas />

        <div v-if="textLayer" class="textLayer" />

        <div v-if="annotationLayer" class="annotationLayer" />
      </div>

      <slot name="after-page" :page="pageNum" />
    </div>
  </div>
</template>

<style lang="scss">
.vue-pdf-embed {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  overflow-x: hidden;

  &__page {
    position: relative;
    margin: 0 auto 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .textLayer {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    opacity: 0.2;
    line-height: 1.0;
    pointer-events: auto;
    user-select: text;
    mix-blend-mode: multiply;

    > span {
      color: transparent;
      position: absolute;
      white-space: pre;
      cursor: text;
      transform-origin: 0% 0%;
      pointer-events: auto;

      &::selection {
        background-color: rgba(0, 0, 255, 0.8);
      }
    }

    .pdf-highlight {
      position: absolute;
      border: 1px solid rgba(255, 170, 0, 0.8);
      border-radius: 2px;
      pointer-events: auto;
      cursor: pointer;
      z-index: 2;
      mix-blend-mode: multiply;
      box-shadow: 0 0 2px rgba(255, 170, 0, 0.3);
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.05);
        box-shadow: 0 0 4px rgba(255, 170, 0, 0.5);
        background-color: rgba(255, 255, 0, 0.8) !important;
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  }

  .annotationLayer {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 3;
  }
}

@keyframes highlight-pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes highlightFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
    background: rgba(255, 255, 0, 0.3);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
    background: rgba(255, 235, 59, 0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%);
  }
}

/* 文字搜索高亮样式 */
.textLayer .pdf-text-highlight {
  background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%) !important;
  color: #1a1a1a !important;
  padding: 2px 4px !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(255, 235, 59, 0.4) !important;
  border: 1px solid #fdd835 !important;
  font-weight: 500 !important;
  position: relative !important;
  display: inline-block !important;
  animation: highlightFadeIn 0.5s ease-out !important;
  z-index: 10 !important;
  transition: all 0.3s ease !important;
  
  &:hover {
    background: linear-gradient(120deg, #ffee58 0%, #ffc107 100%) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.6) !important;
  }
  
  &:first-of-type {
    animation: highlightFadeIn 0.5s ease-out, highlight-pulse 2s ease-in-out 3 !important;
  }
}

/* 新的文字搜索高亮样式 */
.pdf-text-search-highlight {
  position: absolute !important;
  background-color: rgba(255, 255, 0, 0.9) !important;
  border: 1px solid rgba(255, 200, 0, 0.6) !important;
  border-radius: 2px !important;
  pointer-events: none !important;
  z-index: 2 !important;
  transition: all 0.2s ease !important;
  animation: highlightFadeIn 0.3s ease-out !important;
  
  &:hover {
    background-color: rgba(255, 255, 0, 0.6) !important;
    border-color: rgba(255, 200, 0, 0.8) !important;
  }
}
</style>

<style scoped>
.pdf-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.pdf-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.pdf-embed {
  width: 100%;
  height: 100%;
}

.pdf-highlight-container {
  position: absolute;
  background-color: rgba(255, 255, 0, 0.3);
  pointer-events: none;
  z-index: 1;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.pdf-highlight-container:hover {
  background-color: rgba(255, 255, 0, 0.5);
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #f56c6c;
  font-size: 14px;
  gap: 8px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 2;
}

.loading-icon {
  animation: rotate 1s linear infinite;
  margin-right: 8px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
